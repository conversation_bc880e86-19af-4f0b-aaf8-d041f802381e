#!/bin/bash

# Database Migration Script
# This script helps run database migrations with proper environment setup

set -e  # Exit on any error

echo "🚀 FastAPI Database Migration Script"
echo "======================================"

# Set default database URL if not provided
if [ -z "$DATABASE_URL" ]; then
    export DATABASE_URL="postgresql+asyncpg://root:password@127.0.0.1:5432/alice"
    echo "Using default DATABASE_URL: $DATABASE_URL"
else
    echo "Using DATABASE_URL: $DATABASE_URL"
fi

# Function to check if PostgreSQL is running
check_postgres() {
    echo "🔍 Checking PostgreSQL connection..."
    if uv run python -c "
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine

async def test():
    try:
        engine = create_async_engine('$DATABASE_URL')
        async with engine.connect() as conn:
            await conn.execute('SELECT 1')
        await engine.dispose()
        print('✅ PostgreSQL is running and accessible')
        return True
    except Exception as e:
        print(f'❌ PostgreSQL connection failed: {e}')
        return False

import sys
sys.exit(0 if asyncio.run(test()) else 1)
"; then
        return 0
    else
        return 1
    fi
}

# Function to run migrations
run_migrations() {
    echo "📦 Running database migrations..."
    uv run alembic upgrade head
    if [ $? -eq 0 ]; then
        echo "✅ Migrations completed successfully!"
    else
        echo "❌ Migration failed!"
        exit 1
    fi
}

# Function to create new migration
create_migration() {
    if [ -z "$1" ]; then
        echo "❌ Please provide a migration message"
        echo "Usage: $0 create 'Your migration message'"
        exit 1
    fi
    
    echo "📝 Creating new migration: $1"
    uv run alembic revision --autogenerate -m "$1"
    if [ $? -eq 0 ]; then
        echo "✅ Migration created successfully!"
    else
        echo "❌ Failed to create migration!"
        exit 1
    fi
}

# Function to show migration status
show_status() {
    echo "📊 Current migration status:"
    uv run alembic current
    echo ""
    echo "📋 Migration history:"
    uv run alembic history
}

# Main script logic
case "$1" in
    "upgrade"|"")
        if check_postgres; then
            run_migrations
        else
            echo ""
            echo "💡 To start PostgreSQL:"
            echo "   brew services start postgresql"
            echo "   # or"
            echo "   pg_ctl -D /usr/local/var/postgres start"
            echo ""
            echo "💡 To create the database:"
            echo "   createdb alice"
            echo ""
            exit 1
        fi
        ;;
    "create")
        if check_postgres; then
            create_migration "$2"
        else
            echo "❌ PostgreSQL must be running to create migrations"
            exit 1
        fi
        ;;
    "status")
        if check_postgres; then
            show_status
        else
            echo "❌ PostgreSQL must be running to check status"
            exit 1
        fi
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [command] [options]"
        echo ""
        echo "Commands:"
        echo "  upgrade (default)  Run pending migrations"
        echo "  create <message>   Create new migration"
        echo "  status            Show migration status"
        echo "  help              Show this help"
        echo ""
        echo "Environment Variables:"
        echo "  DATABASE_URL      PostgreSQL connection URL"
        echo "                    Default: postgresql+asyncpg://root:password@127.0.0.1:5432/alice"
        echo ""
        echo "Examples:"
        echo "  $0                           # Run migrations"
        echo "  $0 upgrade                   # Run migrations"
        echo "  $0 create 'Add user table'   # Create new migration"
        echo "  $0 status                    # Show migration status"
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo "Run '$0 help' for usage information"
        exit 1
        ;;
esac
