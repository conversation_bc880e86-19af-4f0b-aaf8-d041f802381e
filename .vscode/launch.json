{"version": "0.2.0", "configurations": [{"name": "🚀 Run FastAPI App", "type": "debugpy", "request": "launch", "module": "app.main", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "LOG_FORMAT": "json", "LOG_LEVEL": "INFO", "ENVIRONMENT": "development", "DATABASE_URL": "postgresql+asyncpg://root:password@127.0.0.1:5432/alice", "DATABASE_ECHO": "true"}, "justMyCode": false, "stopOnEntry": false}]}