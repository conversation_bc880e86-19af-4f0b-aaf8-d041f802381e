{"version": "0.2.0", "configurations": [{"name": "🚀 Run FastAPI App (with Trace Server)", "type": "debugpy", "request": "launch", "module": "app.main", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "LOG_FORMAT": "json", "LOG_LEVEL": "INFO", "ENVIRONMENT": "development", "DATABASE_URL": "postgresql+asyncpg://root:password@127.0.0.1:5432/alice", "DATABASE_ECHO": "true", "OTEL_EXPORTER_OTLP_ENDPOINT": "http://127.0.0.1:4317", "OTEL_SERVICE_NAME": "fastapi-template", "OTEL_SERVICE_VERSION": "1.0.0", "OTEL_ENVIRONMENT": "development", "OTEL_CONSOLE_SPANS": "false"}, "justMyCode": false, "stopOnEntry": false}, {"name": "🚀 Run FastAPI App (Console Only)", "type": "debugpy", "request": "launch", "module": "app.main", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "LOG_FORMAT": "json", "LOG_LEVEL": "INFO", "ENVIRONMENT": "development", "DATABASE_URL": "postgresql+asyncpg://root:password@127.0.0.1:5432/alice", "DATABASE_ECHO": "true", "OTEL_SERVICE_NAME": "fastapi-template", "OTEL_SERVICE_VERSION": "1.0.0", "OTEL_ENVIRONMENT": "development", "OTEL_CONSOLE_SPANS": "false"}, "justMyCode": false, "stopOnEntry": false}, {"name": "🔍 Run FastAPI App (Verbose Spans)", "type": "debugpy", "request": "launch", "module": "app.main", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "LOG_FORMAT": "json", "LOG_LEVEL": "INFO", "ENVIRONMENT": "development", "DATABASE_URL": "postgresql+asyncpg://root:password@127.0.0.1:5432/alice", "DATABASE_ECHO": "true", "OTEL_EXPORTER_OTLP_ENDPOINT": "http://127.0.0.1:4317", "OTEL_SERVICE_NAME": "fastapi-template", "OTEL_SERVICE_VERSION": "1.0.0", "OTEL_ENVIRONMENT": "development", "OTEL_CONSOLE_SPANS": "true"}, "justMyCode": false, "stopOnEntry": false}]}