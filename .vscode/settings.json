{
    // Python Configuration
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.terminal.activateEnvironment": true,
    "python.analysis.autoImportCompletions": true,
    "python.analysis.typeCheckingMode": "basic",
    
    // Formatting
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": [
        "--line-length=88",
        "--target-version=py313"
    ],
    
    // Linting
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.linting.flake8Args": [
        "--max-line-length=88",
        "--extend-ignore=E203,W503"
    ],
    
    // Import sorting
    "python.sortImports.args": [
        "--profile=black",
        "--line-length=88"
    ],
    
    // Testing
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "python.testing.pytestArgs": [
        "tests"
    ],
    
    // Editor settings
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    "editor.rulers": [88],
    
    // File associations
    "files.associations": {
        "*.env.example": "properties",
        "*.env.local": "properties",
        "*.env.development": "properties",
        "*.env.production": "properties"
    },
    
    // Exclude files from explorer
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/.pytest_cache": true,
        "**/.mypy_cache": true,
        "**/htmlcov": true,
        "**/.coverage": true
    },
    
    // Search exclude
    "search.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/.pytest_cache": true,
        "**/.mypy_cache": true,
        "**/htmlcov": true,
        "**/.coverage": true,
        "**/uv.lock": true
    },
    
    // Terminal settings
    "terminal.integrated.env.osx": {
        "PYTHONPATH": "${workspaceFolder}"
    },
    "terminal.integrated.env.linux": {
        "PYTHONPATH": "${workspaceFolder}"
    },
    "terminal.integrated.env.windows": {
        "PYTHONPATH": "${workspaceFolder}"
    },
    
    // JSON formatting for logs
    "json.format.enable": true,
    "json.format.keepLines": false,
    
    // Language specific settings
    "[python]": {
        "editor.defaultFormatter": "ms-python.black-formatter",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.organizeImports": true
        }
    },
    
    "[json]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.formatOnSave": true
    },
    
    "[jsonc]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.formatOnSave": true
    },
    
    // FastAPI specific
    "emmet.includeLanguages": {
        "jinja-html": "html"
    }
}
