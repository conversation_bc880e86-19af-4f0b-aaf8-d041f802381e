"""
Trace ID Middleware

This middleware handles trace ID extraction from request headers and generation
of new trace IDs. It also adds trace IDs to response headers and logging context.
"""

import uuid
from typing import Callable
from contextvars import ContextVar

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from opentelemetry import trace
from opentelemetry.trace import set_span_in_context, SpanKind
from opentelemetry.context import attach, detach

from app.config.telemetry import get_tracer
from app.config.log_config import get_logger

logger = get_logger(__name__)

# Context variable to store trace ID for logging
trace_id_context: ContextVar[str] = ContextVar('trace_id', default='')


class TraceIDMiddleware(BaseHTTPMiddleware):
    """
    Middleware to handle trace ID extraction and generation.
    
    This middleware:
    1. Checks for existing X-Trace-ID header in request
    2. If present, uses that trace ID
    3. If not present, generates a new trace ID
    4. Adds trace ID to response headers
    5. Makes trace ID available to logging system
    """
    
    def __init__(self, app, header_name: str = "X-Trace-ID"):
        super().__init__(app)
        self.header_name = header_name
        self.tracer = get_tracer()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request and response with trace ID handling.
        
        Args:
            request: Incoming HTTP request
            call_next: Next middleware/handler in chain
            
        Returns:
            HTTP response with trace ID header
        """
        # Check for existing trace ID in request headers
        existing_trace_id = request.headers.get(self.header_name)
        
        if existing_trace_id:
            # Use existing trace ID from request
            trace_id = existing_trace_id
            logger.debug(f"Using existing trace ID from request: {trace_id}")
        else:
            # Generate new trace ID
            trace_id = self._generate_trace_id()
            logger.debug(f"Generated new trace ID: {trace_id}")
        
        # Set trace ID in context for logging
        trace_id_token = trace_id_context.set(trace_id)
        
        try:
            # Create a span for this request
            with self.tracer.start_as_current_span(
                f"{request.method} {request.url.path}",
                kind=SpanKind.SERVER,
                attributes={
                    "http.method": request.method,
                    "http.url": str(request.url),
                    "http.route": request.url.path,
                    "http.user_agent": request.headers.get("user-agent", ""),
                    "trace.id": trace_id,
                }
            ) as span:
                # Add trace ID as span attribute
                span.set_attribute("trace.id", trace_id)
                
                # Process request
                response = await call_next(request)
                
                # Add trace ID to response headers
                response.headers[self.header_name] = trace_id
                
                # Add response status to span
                span.set_attribute("http.status_code", response.status_code)
                
                logger.info(
                    f"Request processed",
                    extra={
                        "trace_id": trace_id,
                        "method": request.method,
                        "path": request.url.path,
                        "status_code": response.status_code,
                    }
                )
                
                return response
                
        except Exception as e:
            logger.error(
                f"Error processing request: {str(e)}",
                extra={
                    "trace_id": trace_id,
                    "method": request.method,
                    "path": request.url.path,
                },
                exc_info=True
            )
            raise
        finally:
            # Reset trace ID context
            trace_id_context.reset(trace_id_token)
    
    def _generate_trace_id(self) -> str:
        """
        Generate a new trace ID.
        
        Returns:
            New trace ID as hex string
        """
        # Generate a 128-bit trace ID (32 hex characters)
        return uuid.uuid4().hex + uuid.uuid4().hex[:16]


def get_current_trace_id() -> str:
    """
    Get the current trace ID from context.
    
    Returns:
        Current trace ID or empty string if not set
    """
    return trace_id_context.get('')
