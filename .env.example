# FastAPI MVC Template Environment Variables
# Copy this file to .env and update the values for your environment

# Application Settings
PROJECT_NAME="FastAPI MVC Template"
VERSION="1.0.0"
ENVIRONMENT="development"  # development, staging, production
DEBUG=true

# Server Settings
HOST="0.0.0.0"
PORT=8000
ALLOWED_HOSTS=["*"]

# Database Settings
# SQLite (for local development)
# DATABASE_URL="sqlite+aiosqlite:///./fastapi_mvc.db"
# PostgreSQL (recommended)
DATABASE_URL="postgresql+asyncpg://root:password@127.0.0.1:5432/alice"
DATABASE_ECHO=false  # Set to true to log all SQL queries with parameters

# Redis Settings (Optional)
# REDIS_URL="redis://localhost:6379/0"

# Security Settings
SECRET_KEY="your-secret-key-change-this-in-production-make-it-long-and-random"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Email Settings (Optional)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT=587
# SMTP_USERNAME="<EMAIL>"
# SMTP_PASSWORD="your-app-password"
# SMTP_TLS=true

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR="uploads"

# Logging Settings
LOG_LEVEL="INFO"
LOG_FORMAT="json"  # "json" or "standard"
# LOG_FILE="app.log"  # Optional: log to file
# LOG_JSON_INDENT=2  # Optional: pretty print JSON logs (for development)
LOG_INCLUDE_TIMESTAMP=true
LOG_INCLUDE_LEVEL=true
LOG_INCLUDE_LOGGER_NAME=true  # Controls "caller" field (logger.function:line)

# OpenTelemetry Settings
# OTEL_EXPORTER_OTLP_ENDPOINT="http://127.0.0.1:4317"  # Your trace server endpoint
# OTEL_EXPORTER_OTLP_HEADERS=""  # Optional: comma-separated key=value pairs
OTEL_SERVICE_NAME="fastapi-template"
OTEL_SERVICE_VERSION="1.0.0"
OTEL_ENVIRONMENT="development"
OTEL_CONSOLE_SPANS=false  # Set to true to enable verbose console span output

# API Settings
API_V1_PREFIX="/api/v1"
ITEMS_PER_PAGE=20
MAX_ITEMS_PER_PAGE=100
