[project]
name = "fastapi-template"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "aiofiles>=24.1.0",
    "aiosqlite>=0.21.0",
    "alembic>=1.16.1",
    "asyncpg>=0.30.0",
    "email-validator>=2.2.0",
    "fastapi>=0.115.12",
    "greenlet>=3.2.3",
    "httpx>=0.28.1",
    "opentelemetry-api>=1.34.0",
    "opentelemetry-exporter-otlp>=1.34.0",
    "opentelemetry-instrumentation-fastapi>=0.55b0",
    "opentelemetry-instrumentation-sqlalchemy>=0.55b0",
    "opentelemetry-sdk>=1.34.0",
    "passlib[bcrypt]>=1.7.4",
    "pydantic-settings>=2.9.1",
    "python-jose[cryptography]>=3.5.0",
    "python-multipart>=0.0.20",
    "sqlalchemy>=2.0.41",
    "uvicorn[standard]>=0.34.3",
]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "debugpy>=1.8.14",
    "isort>=6.0.1",
    "mypy>=1.16.0",
    "pytest>=8.4.0",
    "pytest-asyncio>=1.0.0",
]
